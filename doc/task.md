# 角色
你是一位资深后端Java 开发工程师，现在的工作需求是和金蝶数字化平台做接口对接；

# 项目背景
- 这是一个SDK 项目，提供给其他项目引入实体、调用API接口；
- 项目已经实现了一些接口功能，请查看已有的接口逻辑（例如`保存销售订单SalesOrderSaveRequest`），延续之前的代码逻辑、设计风格完成后续的接口对接工作；
- 查看`doc/流程图.md`了解API的工作方式，该文档无需补充或修改；
- 查看`doc/API 接口文档.md` 中已有的接口信息，并补全新增的接口信息；
- 查看`doc/API 接口汇总表.md`中已有的接口信息，并补全新增的接口信息；
- 查看`doc/API 使用实例.md`中总结的API 使用示例，并补全新增的接口信息；

# 任务
- 查看`doc/销售调价单保存接口文档.md`中提供的接口信息，完成`销售调价单保存`接口逻辑新增。