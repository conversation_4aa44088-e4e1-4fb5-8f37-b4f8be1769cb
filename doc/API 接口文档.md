 
## 概述  
  
本文档描述了金蝶SDK中所有可用的API接口，包括接口名称、URL路径、请求参数和返回参数。  
  
## 核心调用方法  
  
所有API调用都通过 `KingdeeDefaultOpClient.requestContent(...)` 方法发送请求。  
  
## API接口列表  
  
### 1. 客户管理相关接口  
  
#### 1.1 客户保存  
- **接口名称**: CustomerSaveRequest  
- **URL路径**: `/v2/gjwl/basedata/bd_customer/saveCustomer`  
- **日志模块**: `kd.bd_customer.saveCustomer`  
- **请求方法**: POST  
- **请求参数**: `List<Customer>`  
- **返回参数**: `CustomerSaveResponse`  
- **描述**: 保存客户信息  
  
#### 1.2 客户扩展保存  
- **接口名称**: CustomerSaveExtRequest  
- **URL路径**: `/v2/gjwl/cas/basedata/bd_customer/saveCustomer`  
- **日志模块**: `kd.bd_customer.saveCustomerExt`  
- **请求方法**: POST  
- **请求参数**: `List<Customer>`  
- **返回参数**: `CustomerSaveExtResponse`  
- **描述**: 扩展方式保存客户信息  
  
#### 1.3 客户反审核  
- **接口名称**: CustomerUnAuditRequest  
- **URL路径**: `/v2/gjwl/basedata/bd_customer/unAuditCustomer`  
- **日志模块**: `kd.bd_customer.unAuditCustomer`  
- **请求方法**: POST  
- **请求参数**: `CustomerUnAudit`  
- **返回参数**: `CustomerUnAuditResponse`  
- **描述**: 对客户进行反审核操作  
  
### 2. 供应商管理相关接口  
  
#### 2.1 供应商创建  
- **接口名称**: SupplierCreateRequest  
- **URL路径**: `/v2/basedata/bd_supplier/add`  
- **日志模块**: `kd.bd_supplier.add`  
- **请求方法**: POST  
- **请求参数**: `SupplierCreatePara`  
  - `number`: 供应商编码  
  - `name`: 供应商名称  
  - `entryLinkman`: 联系人分录  
  - `entryBank`: 银行信息分录  
  - `entryGroupStandard`: 分类标准  
  - `entryTax`: 税务资质  
- **返回参数**: `SimpleResponse`  
- **描述**: 创建新的供应商  
  
#### 2.2 供应商保存  
- **接口名称**: SupplierSaveRequest  
- **URL路径**: `/v2/gjwl/cas/basedata/bd_supplier/saveSupplier`  
- **日志模块**: `kd.bd_supplier.saveSupplier`  
- **请求方法**: POST  
- **请求参数**: `SupplierSavePara`  
  - `number`: 供应商编码  
  - `gjwlAppid`: 应用ID  
  - `name`: 供应商名称  
- **返回参数**: `SupplierSaveResponse`  
- **描述**: 保存供应商信息  
  
#### 2.3 供应商批量更新  
- **接口名称**: SupplierUpdateRequest  
- **URL路径**: `/v2/basedata/bd_supplier/batchUpdate`  
- **日志模块**: `kd.bd_supplier.batchUpdate`  
- **请求方法**: POST  
- **请求参数**: `List<SupplierUpdatePara>`  
- **返回参数**: `SimpleResponse`  
- **描述**: 批量更新供应商信息  
  
#### 2.4 供应商反审核  
- **接口名称**: SupplierUnAuditRequest  
- **URL路径**: `/v2/gjwl/basedata/bd_supplier/unAuditSupplier`  
- **日志模块**: `kd.bd_customer.unAuditSupplier`  
- **请求方法**: POST  
- **请求参数**: `UnAuditPara`  
- **返回参数**: `SupplierUnAuditResponse`  
- **描述**: 对供应商进行反审核操作  
  
### 3. 收款相关接口  
  
#### 3.1 收款单新增保存  
- **接口名称**: PaymentRequest  
- **URL路径**: `/v2/gjwl/cas/cas_recbill/saveRecBill`  
- **日志模块**: `kd.cas_recbill.saveRecBill`  
- **请求方法**: POST  
- **请求参数**: `List<PaymentPara>`  
  - `billNo`: 单据编号  
  - `bizDate`: 业务日期  
  - `payerType`: 付款单位类型  
  - `txtDescription`: 摘要  
  - `actRecAmt`: 收款金额  
  - `exchangeRate`: 汇率  
  - `localAmt`: 折本位币  
  - `payerName`: 付款单位名称  
  - `payerAcctBankNum`: 付款账户  
  - `settleTNumber`: 结算号  
  - `payer`: 付款单位ID  
  - `payerFormId`: 付款单位类型标识ID  
  - `payerAccFormId`: 付款账户类型标识ID  
  - `payerAcctBank`: 付款账户ID  
  - `billTypeId`: 单据类型ID  
  - `billTypeNumber`: 单据类型编码  
  - `payerBankName`: 付款银行  
  - `isAgent`: 代理收款  
  - `paymentMode`: 付款方式  
  - `isRefund`: 退款退票业务  
  - `isFullRefund`: 全额退款  
  - `exRateDate`: 汇率日期  
  - `entries`: 收款明细  
  - `entryEntities`: 单据体列表  
  - `infoEntries`: 流水信息列表  
  - `draftBills`: 结算号基础资料列表  
  - `orgId`: 收款组织ID  
  - `orgNumber`: 收款组织编码  
  - `payeeBankId`: 收款银行ID  
  - `payeeBankNumber`: 收款银行编码  
- **返回参数**: `PaymentResponse`  
- **描述**: 新增保存收款单  
- **参考文档**: https://dev.kingdee.com/open/detail/api/1758916852244810752  
  
#### 3.2 收款流水查询  
- **接口名称**: ReceiptRecordQueryRequest  
- **URL路径**: `/v2/gjwl/cas/transDetail/transDetailNewQuery`  
- **日志模块**: `kd.bd_receipt.receiptRecordQuery`  
- **请求方法**: POST  
- **请求参数**: `ReceiptRecordQueryParams`  
  - `compCode`: 公司编码  
- **返回参数**: `DetailQueryResponse`  
- **描述**: 查询收款流水信息  
- **标准格式**: false  
  
#### 3.3 收款流水认领  
- **接口名称**: ReceiptClaimParamsRequest  
- **URL路径**: `/v2/gjwl/cas/recClaimInterface/recClaimInterface`  
- **日志模块**: `kd.bd_receipt.receiptClaim`  
- **请求方法**: POST  
- **请求参数**: `ReceiptClaimParams`  
  - `compCode`: 公司编码  
  - `claimRevInforBean`: 认领信息  
    - `receivablesId`: 收款流水ID  
    - `receivablesNum`: 收款流水编号  
- **返回参数**: `ReceiptClaimResponse`  
- **描述**: 对收款流水进行认领操作  
- **标准格式**: false  
  
#### 3.4 取消认领  
- **接口名称**: CancelClaimRequest  
- **URL路径**: `/v2/gjwl/cas/cancelRecClaim/calcelrecclaiminterface`  
- **日志模块**: `kd.bd_receipt.cancelClaim`  
- **请求方法**: POST  
- **请求参数**: `CancelClaimParams`  
  - `compCode`: 公司编码  
  - `operateType`: 操作类型 (UNCLAIM取消认领, AUDIT认领复核)  
  - `receivablesId`: 收款流水ID  
  - `remark`: 操作原因  
  - `casRecBillId`: 金蝶收款单据ID  
- **返回参数**: `ReceiptClaimResponse`  
- **描述**: 取消收款流水认领  
- **标准格式**: false  
  
### 4. 付款相关接口  
  
#### 4.1 付款申请查询  
- **接口名称**: PayApplyQueryRequest  
- **URL路径**: `/v2/gjwl/ap/payApply/payApplyQuery`  
- **日志模块**: `kd.payApply.query`  
- **请求方法**: POST  
- **请求参数**: `PayApplyQueryParam`  
- **返回参数**: `PayApplyQueryResponse`  
- **描述**: 查询付款申请信息  
  
#### 4.2 付款申请自动创建  
- **接口名称**: PayApplyAutoCreateRequest  
- **URL路径**: `/v2/gjwl/ap/payApply/payApplyAutoCreate`  
- **日志模块**: `kd.payApply.create`  
- **请求方法**: POST  
- **请求参数**: `List<Claim>`  
- **返回参数**: `PayApplyAutoCreateResponse`  
- **描述**: 自动创建付款申请  
  
#### 4.3 金蝶付款单新增  
- **接口名称**: AddKdPaymentRequest  
- **URL路径**: `/v2/gjwl/cas/cas_paybill/savePayBill`  
- **日志模块**: `kd.cas_paybill.addSave`  
- **请求方法**: POST  
- **请求参数**: `List<AddKdPaymentPara>`  
  - `billno`: 单据编号  
  - `bizdate`: 业务日期  
  - `entry`: 分录  
  - `payeetype`: 收款单位类型  
  - `description`: 描述  
- **返回参数**: `AddKdPaymentResponse`  
- **描述**: 新增金蝶付款单  
  
#### 4.4 查询报账单状态
- **接口名称**: ClaimQueryRequest
- **URL路径**: `/v2/gjwl/cas/billSatus/billstatusquery`
- **日志模块**: `kd.cas.billSatus.billStatusQuery`
- **请求方法**: POST
- **请求参数**: `ClaimQueryPara`
  - `claimNos`: 报账单号
- **返回参数**: `PayLineResultResponse`
- **描述**: 查询报账单状态
- **标准格式**: false

### 5. 项目管理相关接口

#### 5.1 项目保存提交审核
- **接口名称**: ProjectSaveRequest
- **URL路径**: `/v2/gjwl/basedata/bd_project/saveProject`
- **日志模块**: `kd.bd_project.saveProject`
- **请求方法**: POST
- **请求参数**: `List<Project>`
  - `id`: 项目ID (修改时需传)
  - `number`: 项目编码 (必填)
  - `name`: 项目名称 (必填)
  - `createorg_number`: 创建组织编码 (必填)
  - `gjwl_appid`: 外部编码 (必填)
  - `gjwl_sourcesystemtype`: 来源系统 (必填, 固定值"广交云供应链管理系统")
- **返回参数**: `ProjectSaveResponse`
- **描述**: 广交云项目审核后推送金蝶云星空旗舰版项目信息

#### 5.2 项目反审核
- **接口名称**: ProjectUnAuditRequest
- **URL路径**: `/v2/basedata/bd_project/batchUnaudit`
- **日志模块**: `kd.bd_project.unAuditProject`
- **请求方法**: POST
- **请求参数**: `ProjectUnAudit`
  - `id`: 项目ID列表 (必填, Array<String>)
- **返回参数**: `ProjectUnAuditResponse`
- **描述**: 广交云项目审核后需修改保存前需反审核金蝶云星空旗舰版项目信息

### 6. 物料管理相关接口

#### 6.1 物料和组织公共信息批量保存提交审核
- **接口名称**: MaterialSaveRequest
- **URL路径**: `/v2/gjwl/gjwl_basedata_ext/saveMaterial`
- **日志模块**: `kd.bd_material.saveMaterial`
- **请求方法**: POST
- **请求参数**: `MaterialSaveRequest.MaterialSaveData`
  - `materials`: 物料数据列表 (`List<MaterialDataVo>`)
    - `materialInfoVo`: 物料主档信息
      - `id`: 物料ID
      - `name`: 物料名称 (必填)
      - `number`: 物料编码
      - `gjwl_appid`: 外部编码 (必填)
      - `createorg`: 创建组织编码 (固定值"gjwl")
      - `baseunit`: 基本单位编码 (必填)
      - `modelnum`: 规格型号
      - `unitconvertdir`: 换算方向 (固定值"A")
      - `entry_groupstandard`: 分类信息分录
    - `commonInfoVo`: 物料组织公共信息
      - `materialtype`: 物料类型 (固定值"1")
      - `materialattr`: 物料属性 (固定值"10040")
      - `enablepur`: 采购信息 (固定值true)
      - `enablesale`: 销售信息 (固定值true)
      - `enableinv`: 库存信息 (固定值true)
      - `group`: 存货类别 (固定值"CHJZFL06-SYS")
- **返回参数**: `MaterialSaveResponse`
- **描述**: 广交云产品审核后推送金蝶云星空旗舰版物料信息

#### 6.2 物料库存信息批量更新
- **接口名称**: MaterialInventoryUpdateRequest
- **URL路径**: `/v2/sbd/bd_materialinventoryinfo/batchUpdate`
- **日志模块**: `kd.bd_material.inventoryUpdate`
- **请求方法**: POST
- **请求参数**: `List<MaterialInventoryUpdatePara>`
  - `enableshelflifemgr`: 保质期管理 (必填)
  - `shelflifeunit`: 保质期单位 (必填, day/month/year)
  - `shelflife`: 保质期
  - `caldirection`: 计算方向 (固定值"4")
  - `startdatecaltype`: 生产日计算方式 (固定值"1")
  - `calculationforenddate`: 到期日计算方式 (固定值"0")
  - `leadtimeunit`: 提前期单位 (必填, day/month/year)
  - `masterid_number`: 物料编码 (必填)
  - `createorg_number`: 库存信息创建组织编码 (固定值"gjwl")
- **返回参数**: `MaterialInventoryUpdateResponse`
- **描述**: 广交云产品新增后，如物料需要开启保质期则调用该接口

### 7. 库存管理相关接口

#### 7.1 盘盈单保存
- **接口名称**: SurplusBillSaveRequest
- **URL路径**: `/v2/gjwl/im/im_surplusbill/saveSurpluseBill`
- **日志模块**: `kd.im_surplusbill.saveSurpluseBill`
- **请求方法**: POST
- **请求参数**: `List<SurplusBill>`
  - `org_number`: 库存组织编码 (固定值"1011")
  - `billno`: 单据编号 (可选)
  - `gjwl_thirdparty_billno`: 外部系统单号 (必填, 传广交云单号)
  - `bookdate`: 记账日期 (必填)
  - `billtype_number`: 单据类型编码 (固定值"im_InvCheckIn_STD_BT_S")
  - `biztype_number`: 业务类型编码 (固定值"350")
  - `biztime`: 业务日期 (必填)
  - `invscheme_number`: 库存事务编码 (固定值"350")
  - `comment`: 备注 (可选)
  - `billentry`: 物料明细 (必填, List<SurplusBillEntry>)
    - `linetype_number`: 行类型编码 (固定值"010")
    - `material_number`: 物料编码 (必填, 广交产品编码)
    - `invstatus_number`: 入库库存状态编码 (固定值"110")
    - `invtype_number`: 入库库存类型编码 (固定值"110")
    - `owner_number`: 货主编码 (固定值"1011")
    - `keeper_number`: 保管者编码 (固定值"1011")
    - `ownertype`: 货主类型 (固定值"bos_org")
    - `keepertype`: 保管者类型 (固定值"bos_org")
    - `unit_number`: 库存单位编码 (必填)
    - `warehouse_number`: 仓库编码 (必填)
    - `qty`: 盘点数量（库存） (可选)
    - `invgainqty`: 盘盈数量（库存） (必填, 传差异数量)
    - `invqtyacc`: 账存数量（库存） (可选)
    - `baseunit_number`: 基本单位编码 (必填, 同库存单位编码)
    - `baseqty`: 盘点数量（基本） (必填, 同盘点数量库存)
    - `lotnumber`: 批号 (可选, 对应广交云批次)
    - `gjwl_product_lotno`: 生产批号 (可选, 对应广交云生产批号)
    - `project_number`: 项目编码 (可选)
    - `producedate`: 生产日期 (可选)
    - `expirydate`: 有效期至 (可选)
    - `entrycomment`: 单据体备注 (可选)
- **返回参数**: `SurplusBillSaveResponse`
- **描述**: 广交云报溢单审核后推送金蝶云星空旗舰版盘盈单

#### 7.2 盘亏单保存
- **接口名称**: DeficitBillSaveRequest
- **URL路径**: `/v2/gjwl/im/im_deficitbill/saveDeficitBill`
- **日志模块**: `kd.im_deficitbill.saveDeficitBill`
- **请求方法**: POST
- **请求参数**: `List<DeficitBill>`
  - `org_number`: 库存组织编码 (必填)
  - `billno`: 单据编号 (可选)
  - `gjwl_thirdparty_billno`: 外部系统单号 (必填, 传广交云单号)
  - `bookdate`: 记账日期 (必填)
  - `billtype_number`: 单据类型编码 (固定值"im_InvCheckOut_STD_BT_S")
  - `biztype_number`: 业务类型编码 (固定值"351")
  - `biztime`: 业务日期 (必填)
  - `invscheme_number`: 库存事务编码 (固定值"351")
  - `dept_number`: 部门编码 (可选)
  - `billentry`: 物料明细 (必填, List<DeficitBillEntry>)
    - `linetype_number`: 行类型编码 (固定值"010")
    - `material_number`: 物料编码 (必填)
    - `outinvstatus_number`: 出库库存状态编码 (固定值"110")
    - `outinvtype_number`: 出库库存类型编码 (固定值"110")
    - `outowner_number`: 货主编码 (必填)
    - `outkeeper_number`: 保管者编码 (必填)
    - `outownertype`: 货主类型 (固定值"bos_org")
    - `outkeepertype`: 保管者类型 (固定值"bos_org")
    - `unit_number`: 库存单位编码 (必填)
    - `warehouse_number`: 仓库编码 (必填)
    - `qty`: 盘点数量（库存） (可选)
    - `invqtyacc`: 账存数量（库存） (可选)
    - `invlossqty`: 盘亏数量（库存） (必填, 传差异数量)
    - `baseunit_number`: 基本单位编码 (必填)
    - `baseqty`: 盘点数量（基本） (必填)
    - `lotnumber`: 批号 (可选, 对应广交云批次)
    - `gjwl_product_lotno`: 生产批号 (可选, 对应广交云生产批号)
- **返回参数**: `DeficitBillSaveResponse`
- **描述**: 广交云报损单(盘亏)审核后推送金蝶云星空旗舰版盘亏单

#### 7.3 分步调出单保存
- **接口名称**: TransOutBillSaveRequest
- **URL路径**: `/v2/gjwl/im/im_transoutbill/SaveTransOutBill`
- **日志模块**: `kd.im_transoutbill.SaveTransOutBill`
- **请求方法**: POST
- **请求参数**: `List<TransOutBill>`
  - `billno`: 单据编号 (可选)
  - `gjwl_thirdparty_billno`: 外部系统单号 (必填, 传广交云单号)
  - `gjwl_sourcesystemtype`: 来源系统 (固定值"广交云供应链管理系统")
  - `biztime`: 业务日期 (必填)
  - `comment`: 备注 (可选)
  - `transtype`: 调拨类型 (固定值"A")
  - `org_number`: 调出组织编码 (固定值"1011")
  - `billtype_number`: 单据类型编码 (固定值"im_AllotOutBill_STD_BT_S")
  - `biztype_number`: 业务类型编码 (固定值"310")
  - `invscheme_number`: 库存事务编码 (固定值"315")
  - `inorg_number`: 调入组织编码 (固定值"1011")
  - `settlescurrency_number`: 本位币货币代码 (固定值"CNY")
  - `billentry`: 物料明细 (必填, List<TransOutBillEntry>)
    - `id`: 物料明细ID (可选)
    - `linetype_number`: 行类型编码 (固定值"010")
    - `material_number`: 物料编码 (必填, 广交产品编码)
    - `unit_number`: 库存单位编码 (可选)
    - `qty`: 物料明细数量 (必填)
    - `warehouse_number`: 调出仓库编码 (必填)
    - `lotnumber`: 批号 (可选, 物料启用批号时必传)
    - `gjwl_product_lotno`: 生产批号 (可选, 对应广交云生产批号)
    - `owner_number`: 调入货主编码 (固定值"1011")
    - `producedate`: 生产日期 (可选, 物料启用保质期时必传)
    - `expirydate`: 到期日期 (可选, 物料启用保质期时必传)
    - `outinvstatus_number`: 调出库存状态编码 (固定值"110")
    - `outinvtype_number`: 调出库存类型编码 (固定值"110")
    - `outownertype`: 调出货主类型 (固定值"bos_org")
    - `outowner_number`: 调出货主编码 (固定值"1011")
    - `outkeepertype`: 调出保管者类型 (固定值"bos_org")
    - `outkeeper_number`: 调出保管者编码 (固定值"1011")
    - `isfreegift`: 赠品标识 (可选)
    - `entrycomment`: 物料明细备注 (可选)
    - `inwarehouse_number`: 调入仓库编码 (可选)
    - `ownertype`: 调入货主类型 (固定值"bos_org")
    - `keepertype`: 调入保管者类型 (固定值"bos_org")
    - `keeper_number`: 调入保管者编码 (固定值"1011")
    - `invstatus_number`: 调入库存状态编码 (固定值"110")
    - `invtype_number`: 调入库存类型编码 (固定值"110")
    - `project_number`: 调出项目编码 (可选)
    - `inproject_number`: 调入项目编码 (可选)
- **返回参数**: `TransOutBillSaveResponse`
- **描述**: 广交云调拨单(调出)审核后推送金蝶云星空旗舰版分步调出单

#### 7.4 分步调入单保存
- **接口名称**: TransInBillSaveRequest
- **URL路径**: `/v2/im/im_transinbill/batchAdd_V2`
- **日志模块**: `kd.im_transinbill.batchAdd_V2`
- **请求方法**: POST
- **请求参数**: `List<TransInBill>`
  - `org_number`: 调入组织编码 (固定值"BU-001")
  - `billno`: 单据编号 (可选)
  - `gjwl_thirdparty_billno`: 外部系统单号 (必填, 传广交云单号)
  - `gjwl_sourcesystemtype`: 来源系统 (固定值"广交云供应链管理系统")
  - `biztime`: 业务日期 (必填)
  - `billtype_number`: 单据类型编码 (固定值"im_AllotInBill_STD_BT_S")
  - `biztype_number`: 业务类型编码 (固定值"310")
  - `invscheme_number`: 库存事务编码 (固定值"316")
  - `transtype`: 调拨类型 (固定值"A")
  - `transit`: 在途归属 (固定值"A")
  - `outorg_number`: 调出组织编码 (固定值"BU-002")
  - `settlescurrency_number`: 本位币货币代码 (固定值"CNY")
  - `comment`: 备注 (可选)
  - `billentry`: 物料明细 (必填, List<TransInBillEntry>)
    - `id`: 物料明细ID (可选)
    - `linetype_number`: 行类型编码 (固定值"010")
    - `material_number`: 物料编码 (必填)
    - `mversion_number`: 物料版本编码 (可选)
    - `unit_number`: 库存单位编码 (必填)
    - `qty`: 物料明细数量 (必填)
    - `warehouse_number`: 调入仓库编码 (必填)
    - `invstatus_number`: 调入库存状态编码 (固定值"110")
    - `ownertype`: 调入货主类型 (固定值"bos_org")
    - `owner_number`: 调入货主编码 (固定值"BU-001")
    - `keepertype`: 调入保管者类型 (固定值"bos_org")
    - `keeper_number`: 调入保管者编码 (固定值"BU-001")
    - `producedate`: 生产日期 (可选, 物料启用保质期时必传)
    - `expirydate`: 有效期至 (可选, 物料启用保质期时必传)
    - `invtype_number`: 调入库存类型编码 (固定值"110")
    - `outinvstatus_number`: 调出库存状态编码 (固定值"114")
    - `outinvtype_number`: 调出库存类型编码 (固定值"110")
    - `outownertype`: 调出货主类型 (固定值"bos_org")
    - `outkeepertype`: 调出保管者类型 (固定值"bos_org")
    - `outowner_number`: 调出货主编码 (必填)
    - `outkeeper_number`: 调出保管者编码 (必填)
    - `outwarehouse_number`: 调出仓库编码 (必填)
- **返回参数**: `TransInBillSaveResponse`
- **描述**: 广交云调拨单(调入)修改操作审核后推送金蝶云星空旗舰版分步调入单

## 通用响应格式
  
### 标准响应格式  
```json  
{  
  "errorCode": "0",  
  "message": "成功",  
  "status": true,  
  "data": {}  
}  
```  
  
### 8. 采购订单管理相关接口

#### 8.1 采购订单保存
- **接口名称**: PurchaseOrderSaveRequest
- **URL路径**: `/v2/gjwl/pm/pm_purorderbill/savePurOrder`
- **日志模块**: `kd.pm_purorderbill.savePurOrder`
- **请求方法**: POST
- **请求参数**: `List<PurchaseOrder>`
- **返回参数**: `PurchaseOrderSaveResponse`
- **描述**: 广交云采购订单新增/修改操作，审核后推送到金蝶云·星空旗舰版采购订单

**请求参数详细说明**:
- `PurchaseOrder`: 采购订单主体信息
  - `id`: 订单ID（修改时需传）
  - `billno`: 单据编号
  - `gjwl_thirdparty_billno`: 外部系统单号
  - `gjwl_sourcesystemtype`: 来源系统（固定值"广交云供应链管理系统"）
  - `org_number`: 采购组织编码（固定值"1011"）
  - `billtype_number`: 单据类型编码（固定值"pm_PurOrderBill_STD_BT_S"）
  - `biztype_number`: 业务类型编码（固定值"110"）
  - `dept_number`: 采购部门编码（固定值"GJEY1102"）
  - `biztime`: 订单日期（必填）
  - `supplier_number`: 订货供应商编码（必填）
  - `paycondition_number`: 付款条件编号（必填）
  - `settlecurrency_number`: 结算币别货币代码（固定值"CNY"）
  - `comment`: 备注
  - `istax`: 含税标识
  - `paymode`: 付款方式（固定值"CREDIT"）
  - `exratedate`: 汇率日期（同业务日期）
  - `exratetable_number`: 汇率表编码（固定值"ERT-01"）
  - `exchangerate`: 汇率（固定值1）
  - `ispayrate`: 按比例(%)
  - `trdbillno`: 第三方业务编码（传外部系统单号）
  - `billentry`: 物料明细列表
  - `purbillentry_pay`: 付款计划列表
- `PurchaseOrderEntry`: 采购订单明细信息
  - `id`: 明细ID（修改时需传）
  - `linetype_number`: 行类型编码（固定值"010"）
  - `material_number`: 物料编码
  - `unit_number`: 采购单位编码
  - `qty`: 数量（必填）
  - `price`: 单价
  - `priceandtax`: 含税单价
  - `warehouse_number`: 仓库编码
  - `discounttype`: 折扣方式（A:折扣率%, B:单位折扣额, NULL:无）
  - `discountrate`: 单位折扣率
  - `discountamount`: 折扣额
  - `taxrateid_number`: 税率编码
  - `entryreqorg_number`: 需求组织编码（固定值"1011"）
  - `entryrecorg_number`: 收料组织编码（固定值"1011"）
  - `entrysettleorg_number`: 结算组织编码（固定值"1011"）
  - `ownertype`: 货主类型（固定值"bos_org"）
  - `owner_number`: 货主编码（固定值"1011"）
  - `promisedate`: 承诺日期
  - `deliverdate`: 交货日期（必填）
  - `ispresent`: 赠品标识
  - `project_number`: 项目编码
  - `entrycomment`: 明细备注
- `PurchaseOrderPayEntry`: 付款计划信息
  - `id`: 付款计划ID
  - `planentrysettleorg_number`: 结算组织编码（固定值"1011"）
  - `paydate`: 到期日
  - `payrate`: 应付比例(%)
  - `payamount`: 应付金额
  - `isprepay`: 是否预付

**返回参数详细说明**:
- `PurchaseOrderSaveResponseData`: 响应数据
  - `result`: 保存结果列表
  - `failCount`: 操作失败数量
  - `successCount`: 操作成功数量
- `PurchaseOrderSaveResult`: 保存结果项
  - `billIndex`: 单据索引
  - `billStatus`: 单据状态
  - `errors`: 错误信息数组
  - `id`: 单据ID（重要：后续修改和下推接口需用）
  - `keys`: 主键信息
  - `number`: 单据编号
  - `type`: 操作类型

### 9. 销售订单管理相关接口

#### 9.1 销售订单保存
- **接口名称**: SalesOrderSaveRequest
- **URL路径**: `/v2/gjwl/sm/sm_salorder/saveSalOrder`
- **日志模块**: `kd.sm_salorder.saveSalOrder`
- **请求方法**: POST
- **请求参数**: `List<SalesOrder>`
- **返回参数**: `SalesOrderSaveResponse`
- **描述**: 广交云销售订单新增/修改操作，审核后推送到金蝶云·星空旗舰版销售订单

**请求参数详细说明**:
- `SalesOrder`: 销售订单主体信息
  - `id`: 订单ID（修改时需传）
  - `bizdate`: 订单日期（必填）
  - `org_number`: 销售组织编码（固定值"1011"）
  - `customer_number`: 订货客户编码（必填）
  - `billtype_number`: 单据类型编码（固定值"sm_SalesOrder_STD_BT_S"）
  - `biztype_number`: 业务类型编码（固定值"210"）
  - `settlecurrency_number`: 结算币别货币代码（固定值"CNY"）
  - `gjwl_thirdparty_billno`: 外部系统单号（广交云订单编号）
  - `gjwl_sourcesystemtype`: 来源系统（固定值"广交云供应链管理系统"）
  - `comment`: 表头备注
  - `istax`: 含税标识
  - `iswholediscount`: 录入整单折扣标识
  - `wholediscountamount`: 整单折扣额
  - `reccondition_number`: 收款条件编号
  - `billentry`: 物料明细列表
- `SalesOrderEntry`: 销售订单明细信息
  - `id`: 明细ID（修改时需传）
  - `linetype_number`: 行类型编码（固定值"010"）
  - `material_number`: 物料编码（广交云产品编码）
  - `unit_number`: 销售单位编码
  - `e_stockorg_number`: 发货组织编码（固定值"1011"）
  - `entrysettleorg_number`: 结算组织编码（固定值"1011"）
  - `price`: 单价
  - `priceandtax`: 含税单价
  - `qty`: 数量（必填）
  - `taxrateid_number`: 税率编码
  - `taxamount`: 税额
  - `discounttype`: 折扣方式（A:折扣率%, B:单位折扣额, NULL:无）
  - `discountrate`: 单位折扣率
  - `discountamount`: 折扣额
  - `remark`: 备注
  - `warehouse_number`: 仓库编码
  - `amountandtax`: 价税合计
  - `amount`: 金额
  - `ispresent`: 赠品标识
  - `project_number`: 项目编码
  - `lotnumber`: 批号
  - `gjwl_product_lotno`: 生产批号

**返回参数详细说明**:
- `SalesOrderSaveResponseData`: 响应数据
  - `result`: 保存结果列表
  - `failCount`: 操作失败数量
  - `successCount`: 操作成功数量
- `SalesOrderSaveResult`: 保存结果项
  - `billIndex`: 单据索引
  - `billStatus`: 单据状态
  - `errors`: 错误信息数组
  - `id`: 单据ID（重要：后续修改和下推接口需用）
  - `keys`: 主键信息
  - `number`: 单据编号
  - `type`: 操作类型

#### 9.2 销售出库退货单保存
- **接口名称**: SalesOutBillSaveRequest
- **URL路径**: `/v2/gjwl/im/im_saloutbill/saveSaloutBill`
- **日志模块**: `kd.im_saloutbill.saveSaloutBill`
- **请求方法**: POST
- **请求参数**: `List<SalesOutBill>`
- **返回参数**: `SalesOutBillSaveResponse`
- **描述**: 广交云销售出库单/销售退货单修改操作，审核后推送到金蝶云·星空旗舰版销售出库单/销售退货单

#### 9.3 销售调价单保存
- **接口名称**: SalPriceAdjustSaveRequest
- **URL路径**: `/v2/gjwl/im/gjwl_salpriceadjust/saveSalPriceAdjust`
- **日志模块**: `kd.gjwl_salpriceadjust.saveSalPriceAdjust`
- **请求方法**: POST
- **请求参数**: `List<SalPriceAdjust>`
- **返回参数**: `SalPriceAdjustSaveResponse`
- **描述**: 广交云销售调价单审核后推送到金蝶云·星空旗舰版销售调价单

**注意**: 销售出库单与销售退货单使用同一个接口，通过单据类型编码区分：
- 销售出库单：`im_SalOutBill_STD_BT_S`
- 销售退货单：`im_SalOutBill_STD_BT_S_R`

**请求参数详细说明**:
- `SalesOutBill`: 销售出库退货单主体信息
  - `id`: 单据ID（修改时需传）
  - `org_number`: 库存组织编码（固定值"1011"）
  - `billno`: 单据编号
  - `gjwl_thirdparty_billno`: 外部系统单号（传广交云单号）
  - `gjwl_sourcesystemtype`: 来源系统（固定值"广交云供应链管理系统"）
  - `billtype_number`: 单据类型编码（销售出库单-im_SalOutBill_STD_BT_S, 销售退货单-im_SalOutBill_STD_BT_S_R）
  - `biztype_number`: 业务类型编码（210-物料类销售, 2101-物料类销售退货）
  - `invscheme_number`: 库存事务编码（210-物料类销售, 2101-普通销售退、补货）
  - `biztime`: 业务日期
  - `exratedate`: 汇率日期（同业务日期）
  - `customer_number`: 客户编码
  - `paymode`: 付款方式（CREDIT-赊销、CASH-现销）
  - `bizorg_number`: 销售组织编码（固定值"1011"）
  - `currency_number`: 本位币货币代码（固定值"CNY"）
  - `settlecurrency_number`: 结算币别货币代码（固定值"CNY"）
  - `exratetable_number`: 汇率表编码（固定值"ERT-01"）
  - `exchangerate`: 汇率（固定值1）
  - `istax`: 含税标识
  - `reccondition_number`: 收款条件编号
  - `iswholediscount`: 录入整单折扣标识
  - `wholediscountamount`: 整单折扣额
  - `comment`: 备注
  - `billentry`: 物料明细列表
- `SalesOutBillEntry`: 销售出库退货单明细信息
  - `id`: 明细ID（修改时需传）
  - `linetype_number`: 行类型编码（固定值"010"）
  - `material_number`: 物料编码（产品编码）
  - `unit_number`: 库存单位编码
  - `baseunit_number`: 基本单位编码（同库存单位编码）
  - `qty`: 数量
  - `baseqty`: 基本数量（同数量）
  - `warehouse_number`: 仓库编码
  - `outinvtype_number`: 出库库存类型编码（固定值"110"）
  - `outinvstatus_number`: 出库库存状态编码（固定值"110"）
  - `outownertype`: 出库货主类型（固定值"bos_org"）
  - `outowner_number`: 出库货主编码（固定值"1011"）
  - `outkeepertype`: 出库保管者类型（固定值"bos_org"）
  - `outkeeper_number`: 出库保管者编码（固定值"1011"）
  - `entrysettleorg_number`: 结算组织编码（固定值"1011"）
  - `price`: 单价
  - `priceandtax`: 含税单价
  - `taxrateid_number`: 税率编码
  - `taxamount`: 税额
  - `amountandtax`: 价税合计
  - `amount`: 金额
  - `ispresent`: 赠品标识（默认false，金额为0传true）
  - `discounttype`: 折扣方式（A:折扣率%, NULL:无, B:单位折扣额）
  - `discountrate`: 单位折扣率
  - `discountamount`: 折扣额
  - `entrycomment`: 明细备注
  - `project_number`: 项目编码
  - `producedate`: 生产日期（物料启用保质期时必传）
  - `expirydate`: 到期日期（物料启用保质期时必传）
  - `lotnumber`: 批号（物料启用批号时必传）
  - `gjwl_product_lotno`: 生产批号

**返回参数详细说明**:
- `SalesOutBillSaveResponseData`: 响应数据
  - `result`: 保存结果列表
  - `failCount`: 操作失败数量
  - `successCount`: 操作成功数量
- `SalesOutBillSaveResult`: 保存结果项
  - `billIndex`: 单据索引
  - `billStatus`: 单据状态
  - `errors`: 错误信息数组
  - `id`: 单据ID（重要：后续修改和下推接口需用）
  - `keys`: 主键信息
  - `number`: 单据编号
  - `type`: 操作类型

**请求参数详细说明（销售调价单）**:
- `SalPriceAdjust`: 销售调价单主体信息
  - `id`: 单据ID（修改时需传）
  - `billno`: 单据编号
  - `org_number`: 组织编码（固定值"1011"）
  - `gjwl_date`: 业务日期（必填）
  - `gjwl_customer_number`: 客户编码（必填）
  - `gjwl_reason`: 调价原因（必填）
  - `gjwl_thirdparty_billno`: 外部系统单号（必填）
  - `gjwl_sourcesystemtype`: 来源系统（固定值"广交云供应链管理系统"）
  - `entryentity`: 物料明细列表（必填）
- `SalPriceAdjustEntry`: 销售调价单明细信息
  - `id`: 明细ID（修改时需传）
  - `gjwl_salorderbillno`: 销售单号
  - `gjwl_billno`: 单据编号
  - `gjwl_billtype`: 单据类型
  - `gjwl_materiel_number`: 物料编码
  - `gjwl_product_lotno`: 生产批号/序列号
  - `gjwl_taxrate_number`: 税率编码（必填）
  - `gjwl_qty`: 数量
  - `gjwl_oldtaxprice`: 原单价（含税）
  - `gjwl_newesttaxprice`: 调整前最新价（含税）
  - `gjwl_taxprice`: 本次调整单价（含税）
  - `gjwl_adjustamount`: 调整金额

**返回参数详细说明（销售调价单）**:
- `SalPriceAdjustSaveResponseData`: 响应数据
  - `result`: 保存结果列表
  - `failCount`: 操作失败数量
  - `successCount`: 操作成功数量
- `SalPriceAdjustSaveResult`: 保存结果项
  - `billIndex`: 单据索引
  - `billStatus`: 单据状态
  - `errors`: 错误信息数组
  - `id`: 单据ID（重要：后续修改和下推接口需用）
  - `keys`: 主键信息
  - `number`: 单据编号
  - `type`: 操作类型

### 10. 采购调价单管理相关接口

#### 10.1 采购调价单保存
- **接口名称**: PurPriceAdjustSaveRequest
- **URL路径**: `/v2/gjwl/im/gjwl_purpriceadjust/savePurPriceAdjust`
- **日志模块**: `kd.gjwl_purpriceadjust.savePurPriceAdjust`
- **请求方法**: POST
- **请求参数**: `List<PurPriceAdjust>`
- **返回参数**: `PurPriceAdjustSaveResponse`
- **描述**: 广交云.【入库调价单】.审核后 推送 金蝶云·星空旗舰版.【采购调价单】

**请求参数详细说明（采购调价单）**:
- `PurPriceAdjust`: 采购调价单主体信息
  - `id`: 单据ID（修改时需传）
  - `billno`: 单据编号
  - `org_number`: 组织编码（固定值"1011"）
  - `gjwl_date`: 业务日期（必填）
  - `gjwl_type`: 调价类型（必填，0：入库调价，1：返利调价）
  - `gjwl_instockbillno`: 采购入库单号（必填）
  - `gjwl_reason`: 调价原因（必填）
  - `gjwl_thirdparty_billno`: 外部系统单号（必填）
  - `gjwl_sourcesystemtype`: 来源系统（固定值"广交云供应链管理系统"）
  - `gjwl_supplier_number`: 供应商编码（必填）
  - `entryentity`: 物料明细列表（必填）
- `PurPriceAdjustEntry`: 采购调价单明细信息
  - `id`: 明细ID（修改时需传）
  - `gjwl_instockbillno`: 采购入库单号
  - `gjwl_billno`: 单据编号
  - `gjwl_billtype`: 单据类型
  - `gjwl_materiel_number`: 物料编码
  - `gjwl_product_lotno`: 生产批号/序列号
  - `gjwl_taxrate_number`: 税率编码（必填）
  - `gjwl_qty`: 数量
  - `gjwl_oldtaxprice`: 原单价（含税）
  - `gjwl_newesttaxprice`: 调整前最新价（含税）
  - `gjwl_taxprice`: 本次调整单价（含税）
  - `gjwl_adjustamount`: 调整金额

**返回参数详细说明（采购调价单）**:
- `PurPriceAdjustSaveResponseData`: 响应数据
  - `result`: 保存结果列表
  - `failCount`: 操作失败数量
  - `successCount`: 操作成功数量
- `PurPriceAdjustSaveResult`: 保存结果项
  - `billIndex`: 单据索引
  - `billStatus`: 单据状态
  - `errors`: 错误信息数组
  - `id`: 单据ID（重要：后续修改和下推接口需用）
  - `keys`: 主键信息
  - `number`: 单据编号
  - `type`: 操作类型

### 11. 其他出库单管理相关接口

#### 11.1 其他出库单保存
- **接口名称**: OtherOutBillSaveRequest
- **URL路径**: `/v2/gjwl/im/im_otheroutbill/saveOtherOutBill`
- **日志模块**: `kd.im_otheroutbill.saveOtherOutBill`
- **请求方法**: POST
- **请求参数**: `List<OtherOutBill>`
- **返回参数**: `OtherOutBillSaveResponse`
- **描述**: 广交云报损单(不合格报损)审核后推送到金蝶云·星空旗舰版其他出库单

**请求参数详细说明**:
- `OtherOutBill`: 其他出库单主体信息
  - `org_number`: 库存组织编码（固定值"1011"）
  - `billno`: 单据编号（可选）
  - `gjwl_thirdparty_billno`: 外部系统单号（必填，传广交云单号）
  - `gjwl_sourcesystemtype`: 来源系统（固定值"广交云供应链管理系统"）
  - `biztime`: 业务日期（必填）
  - `bookdate`: 记账日期（可选）
  - `billtype_number`: 单据类型编码（固定值"im_OtherOutBill_STD_BT_S"）
  - `biztype_number`: 业务类型编码（固定值"355"）
  - `invscheme_number`: 库存事务编码（固定值"355"）
  - `bizdept_number`: 领用部门编码（固定值"1011EY11EY1102"）
  - `comment`: 备注（可选）
  - `billentry`: 物料明细列表（必填）
- `OtherOutBillEntry`: 其他出库单明细信息
  - `linetype_number`: 行类型编码（固定值"010"）
  - `material_number`: 物料编码（必填，广交产品编码）
  - `unit_number`: 库存单位编码（必填）
  - `qty`: 物料明细数量（必填）
  - `warehouse_number`: 仓库编码（必填）
  - `outinvtype_number`: 出库库存类型编码（固定值"110"）
  - `outinvstatus_number`: 出库库存状态编码（固定值"110"）
  - `outownertype`: 出库货主类型（固定值"bos_org"）
  - `outowner_number`: 出库货主编码（固定值"1011"）
  - `outkeepertype`: 出库保管者类型（固定值"bos_org"）
  - `outkeeper_number`: 出库保管者编码（固定值"1011"）
  - `project_number`: 项目号项目编码（可选）
  - `lotnumber`: 批号（可选，物料启用批号时必传）
  - `gjwl_product_lotno`: 生产批号（可选，对应广交云生产批号）
  - `producedate`: 生产日期（可选，物料启用保质期时必传）
  - `expirydate`: 到期日期（可选，物料启用保质期时必传）
  - `invtype_number`: 入库库存类型编码（固定值"110"）
  - `invstatus_number`: 入库库存状态编码（固定值"110"）
  - `ownertype`: 货主类型（固定值"bos_org"）
  - `owner_number`: 货主编码（固定值"1011"）
  - `keepertype`: 保管者类型（固定值"bos_org"）
  - `keeper_number`: 保管者编码（固定值"1011"）
  - `price`: 价格（可选）
  - `entrycomment`: 分录备注（可选）

**返回参数详细说明**:
- `ResponseData<ResultItem>`: 标准响应数据结构
  - `result`: 保存结果列表
  - `failCount`: 操作失败数量
  - `successCount`: 操作成功数量
- `ResultItem`: 保存结果项
  - `billIndex`: 单据索引
  - `billStatus`: 单据状态
  - `errors`: 错误信息数组
  - `id`: 单据ID
  - `keys`: 主键信息
  - `number`: 单据编号
  - `type`: 操作类型

### 11. 采购入库退料单管理相关接口

#### 11.1 采购入库退料单保存
- **接口名称**: PurInBillSaveRequest
- **URL路径**: `/v2/gjwl/im/im_purinbill/savePruinBill`
- **日志模块**: `kd.im_purinbill.savePruinBill`
- **请求方法**: POST
- **请求参数**: `List<PurInBill>`
- **返回参数**: `PurInBillSaveResponse`
- **描述**: 广交云采购入库单/采退出库单修改操作，审核后推送到金蝶云·星空旗舰版采购入库单/采购退料单

**注意**: 采购入库单与采购退料单使用同一个接口，通过单据类型编码区分：
- 采购入库单：`im_PurInBill_STD_BT_S`
- 采购退料单：`im_PurInBill_STD_BT_S_R`

**请求参数详细说明**:
- `PurInBill`: 采购入库退料单主体信息
  - `id`: 单据ID（修改时需传）
  - `org_number`: 库存组织编码（固定值"1011"）
  - `billno`: 单据编号
  - `gjwl_thirdparty_billno`: 外部系统单号（传广交云单号）
  - `gjwl_sourcesystemtype`: 来源系统（固定值"广交云供应链管理系统"）
  - `billtype_number`: 单据类型编码（采购入库单-im_PurInBill_STD_BT_S, 采购退料单-im_PurInBill_STD_BT_S_R）
  - `biztime`: 业务日期（必填）
  - `exratedate`: 汇率日期（同业务日期）
  - `exratetable_number`: 汇率表编码（固定值"ERT-01"）
  - `exchangerate`: 汇率（固定值1）
  - `paymode`: 付款方式（CREDIT-赊购）
  - `bizorg_number`: 采购组织编码（固定值"1011"）
  - `biztype_number`: 业务类型编码（110-物料类采购, 1101-物料类采购退货）
  - `invscheme_number`: 库存事务编码（110-物料类采购, 1101-普通采购退、补货）
  - `supplier_number`: 供应商编码（必填）
  - `currency_number`: 本位币货币代码（固定值"CNY"）
  - `settlecurrency_number`: 结算币别货币代码（固定值"CNY"）
  - `paycondition_number`: 付款条件编号（必填）
  - `comment`: 备注
  - `bookdate`: 记账日期
  - `istax`: 含税标识
  - `billentry`: 物料明细列表（必填）
- `PurInBillEntry`: 采购入库退料单明细信息
  - `id`: 明细ID（修改时需传）
  - `returnmaterialtype`: 退料类型（如为采购退料单时传"1"）
  - `unit_number`: 库存单位编码（必填）
  - `warehouse_number`: 仓库编码（必填）
  - `invstatus_number`: 入库库存状态编码（固定值"110"）
  - `ownertype`: 入库货主类型（固定值"bos_org"）
  - `owner_number`: 入库货主编码（固定值"1011"）
  - `keepertype`: 入库保管者类型（固定值"bos_org"）
  - `keeper_number`: 入库保管者编码（固定值"1011"）
  - `invtype_number`: 入库库存类型编码（固定值"110"）
  - `linetype_number`: 行类型编码（固定值"010"）
  - `qty`: 数量（必填）
  - `material_number`: 物料编码（必填，广交产品编码）
  - `baseunit_number`: 基本单位编码（同库存单位编码）
  - `baseqty`: 基本数量（同数量）
  - `taxrateid_number`: 税率编码
  - `taxamount`: 税额
  - `amountandtax`: 价税合计
  - `amount`: 金额
  - `ispresent`: 赠品标识（默认false，金额为0传true）
  - `discounttype`: 折扣方式（A:折扣率%, NULL:无, B:单位折扣额）
  - `discountrate`: 单位折扣率
  - `discountamount`: 折扣额
  - `entrycomment`: 明细备注
  - `project_number`: 项目编码
  - `producedate`: 生产日期（物料启用保质期时必传）
  - `expirydate`: 到期日期（物料启用保质期时必传）
  - `lotnumber`: 批号（物料启用批号时必传）
  - `gjwl_product_lotno`: 生产批号
  - `price`: 单价
  - `priceandtax`: 含税单价
  - `remark`: 备注

**返回参数详细说明**:
- `PurInBillSaveResponseData`: 响应数据
  - `result`: 保存结果列表
  - `failCount`: 操作失败数量
  - `successCount`: 操作成功数量
- `PurInBillSaveResult`: 保存结果项
  - `billIndex`: 单据索引
  - `billStatus`: 单据状态
  - `errors`: 错误信息数组
  - `id`: 单据ID（重要：后续修改和下推接口需用）
  - `keys`: 主键信息
  - `number`: 单据编号
  - `type`: 操作类型

### 12. 下推并保存相关接口

#### 12.1 下推并保存
- **接口名称**: PushAndSaveRequest
- **URL路径**: `/v2/gjwl/msbd/pushAndSave`
- **日志模块**: `kd.msbd.pushAndSave`
- **请求方法**: POST
- **请求参数**: `PushAndSaveRequestParam`
  - `sourceEntityNumber`: 源单单据标识 (必填)
    - 销售订单下推销售出库单时：传"sm_salorder"
    - 销售出库单下推销售退货单时：传"im_saloutbill"
    - 采购订单下推采购入库单时：传"pm_purorderbill"
    - 采购入库单下推采购退料单时：传"im_purinbill"
    - 分步调出单下推分步调入单时：传"im_transoutbill"
  - `targetEntityNumber`: 目标单单据标识 (必填)
    - 销售订单下推销售出库单时：传"im_saloutbill"
    - 销售出库单下推销售退货时：传"im_saloutbill"
    - 采购订单下推采购入库单时：传"im_purinbill"
    - 采购入库单下推采购退料单时：传"im_purinbill"
    - 分步调出单下推分步调入单时：传"im_transinbill"
  - `ruleId`: 转换规则ID (必填)
    - 销售订单下推销售出库单时：传"610056021305574400"
    - 销售出库单下推销售退货单时：传"705622291240812544"
    - 采购订单下推采购入库单时：传"565033700123759616"
    - 采购入库单下推采购退料单时：传"705591916980436992"
    - 分步调出单下推分步调入单时：传"475190152356975616"
  - `sourceBills`: 源单单据信息 (必填, List<SelectedBill>)
    - `billId`: 源单ID (必填)
    - `entryIds`: 源单分录ID集合 (可选, 为空则整单下推)
- **返回参数**: `PushAndSaveResponse`
  - `status`: 状态 (true-成功, false-失败)
  - `errorCode`: 错误代码
  - `message`: 错误信息
  - `data`: 下推结果 (PushApiResult)
    - `result`: 返回结果详细信息
    - `pushResult`: 下推并保存返回结果详细信息
      - `pushSuccess`: 下推是否成功
      - `pushFailMessage`: 下推失败整体提示
      - `pushFailReports`: 源单下推失败报告
      - `saveSuccess`: 目标单保存是否成功
      - `saveFailMessage`: 目标单保存失败整体提示
      - `saveFailReports`: 目标单保存失败报告
      - `targetBills`: 生成的目标单信息
    - `targetResult`: 下推并保存返回目标单据详细信息
      - `fid`: 目标单据ID (需记录，用于修改单据)
      - `fbillno`: 目标单据编号
      - `fseq`: 序号
      - `fentryid`: 目标单据分录ID (需记录，用于修改单据)
      - `fmasterid`: 物料ID
      - `fnumber`: 物料编码
- **描述**: 用于单据下推的场景，支持销售订单下推销售出库单、销售出库单下推销售退货单、采购订单下推采购入库单、采购入库单下推采购退料单、分步调出单下推分步调入单等场景
- **注意**: 调用该接口后单据为暂存状态，还需要调用对应的保存接口才会执行保存提交审核操作

### 13. 信用管理相关接口

#### 13.1 查询信用状况
- **接口名称**: CreditStatusQueryRequest
- **URL路径**: `/v2/ccm/credit/getCreditBalance`
- **日志模块**: `kd.ccm.credit.getCreditBalance`
- **请求方法**: POST
- **请求参数**: `CreditStatusQueryParam`
  - `currencynumber`: 币别编码 (可选, String) - 值为具体的币别编码，默认"CNY"
  - `orgscope`: 控制组织范围 (可选, String) - 固定值"SINGLE"
  - `orgnumberset`: 授信组织编码集合 (可选, Set<String>) - 默认["1011"]
  - `dimensionnumber`: 信控维度编码 (必填, String) - 固定值"CUSTOMER"
  - `roletype0`: 维度成员类型0 (可选, String) - 固定值"bd_customer"
  - `rolenumberset0`: 维度成员0编码集合 (可选, Set<String>) - 客户编码集合
  - `schemenumber`: 信用控制方案编码 (可选, String) - 固定值"XKFA-SYS-001"
- **返回参数**: `CreditStatusQueryResponse`
  - `data`: 信用状况数据列表 (List<CreditStatusQueryResponseData>)
    - `archiveidSet`: 信用档案ID (Set<Long>)
    - `orgscope`: 控制组织范围 (String)
    - `archiveorgnumberlist`: 授信组织 (List<String>)
    - `archiveobjectmap`: 信用对象 (HashMap<String, String>)
    - `schemenumber`: 信用控制方案 (String)
    - `dimensionnumber`: 信控维度 (String)
    - `currencynumber`: 币别 (String)
    - `singlecurcontrol`: 币别隔离 (Boolean)
    - `quotaamount`: 信用额度 (BigDecimal)
    - `tempamount`: 临时信用额度 (BigDecimal)
    - `occupyamount`: 实际占用额度 (BigDecimal)
    - `balance`: 可用额度 (BigDecimal)
    - `quotaoverdays`: 信用天数 (BigDecimal)
    - `tempoverdays`: 临时信用天数 (BigDecimal)
    - `actualoverdays`: 逾期天数 (BigDecimal)
    - `overdaysbal`: 超标逾期天数 (BigDecimal)
    - `quotaoveramount`: 允许逾期额度 (BigDecimal)
    - `tempoveramount`: 临时逾期额度 (BigDecimal)
    - `actualoveramount`: 实际逾期额度 (BigDecimal)
    - `overamountbal`: 超标逾期金额 (BigDecimal)
    - `exceed`: 是否超标 (Boolean)
    - `grade`: 信用等级 (String)
    - `quoarchivemap`: 额度类型档案ID (HashMap<String, Long>)
    - `exratetablenumber`: 汇率表 (String)
- **描述**: 广交云销售订单调用金蝶云星空旗舰版信用状况表接口查询客户信用状况
- **标准格式**: false

### 14. 附件管理相关接口

#### 14.1 附件上传
- **接口名称**: AttachmentUploadRequest
- **URL路径**: `/v2/frame/attachment/uploadFile`
- **日志模块**: `kd.frame.attachment.uploadFile`
- **请求方法**: POST
- **请求类型**: multipart/form-data
- **请求参数**: `AttachmentUploadRequestParam`
  - `attachmentUploadFileArgs`: 上传绑定附件信息 (必填, AttachmentUploadFileArgs)
    - `entityNumber`: 源实体标识 (必填, String) - 如为其他出库单传"im_otheroutbill"，如为盘亏单传"im_deficitbill"，如为付款申请单传"ap_payapply"
    - `billPkId`: 单据主键，即单据Id (必填, Object)
    - `entryPkId`: 单据体行主键 (可选, Object)
    - `subEntryPkId`: 子单据体行主键 (可选, Object)
    - `extFormNumber`: 附件控件在扩展表单上添加时需填写扩展表单标识 (可选, String)
    - `controlKey`: 附件上传到控件(附件字段、附件面板)的标识 (必填, String)
    - `description`: 附件备注 (可选, String)
    - `appNumber`: 应用编码 (可选, String)
  - `file`: 文件对象 (必填, File) - 支持本地文件或输入流
- **返回参数**: `AttachmentUploadResponse`
  - `data`: 附件上传结果 (AttachmentUploadFileResult)
    - `path`: 文件路径 (String)
- **描述**: 用于上传附件并绑定附件到对应单据的接口
- **标准格式**: false

### 错误码说明
- `0`: 成功
- `400`: 参数检查错误
- `401`: Token检查签名错误
- `403`: 禁止访问错误
- `404`: API错误
- `601`: 数据重复错误
- `999`: 未识别异常
  
## 请求头信息  
- `Content-Type`: application/json  
- `accesstoken`: 访问令牌  
- `x-acgw-identity`: 身份标识  
- `Idempotency-Key`: 幂等性键值(UUID)  
  
## 注意事项  
1. 所有请求都需要有效的访问令牌  
2. 请求体格式为JSON  
3. 部分接口支持标准格式包装，部分不支持  
4. 所有接口都会记录详细的日志信息  
5. 支持超时设置：连接超时60秒，读取超时60秒，写入超时60秒