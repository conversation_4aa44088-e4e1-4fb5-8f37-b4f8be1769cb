package com.polarbear.kd.api.purpriceadjust;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 采购调价单实体类
 * 
 * <AUTHOR>
 * @apiNote 广交云.【入库调价单】.审核后 推送 金蝶云·星空旗舰版.【采购调价单】
 * @date 2025-09-25
 */
@Data
public class PurPriceAdjust {
    
    /**
     * id (修改时需传)
     */
    @JsonProperty("id")
    private String id;
    
    /**
     * 单据编号
     */
    @JsonProperty("billno")
    private String billno;
    
    /**
     * 组织.编码 (固定值，传"1011")
     */
    @JsonProperty("org_number")
    private String orgNumber;
    
    /**
     * 业务日期
     */
    @JsonProperty("gjwl_date")
    private String gjwlDate;
    
    /**
     * 调价类型
     * 0：入库调价
     * 1：返利调价
     */
    @JsonProperty("gjwl_type")
    private String gjwlType;
    
    /**
     * 采购入库单号
     */
    @JsonProperty("gjwl_instockbillno")
    private String gjwlInstockbillno;
    
    /**
     * 调价原因
     */
    @JsonProperty("gjwl_reason")
    private String gjwlReason;
    
    /**
     * 外部系统单号
     */
    @JsonProperty("gjwl_thirdparty_billno")
    private String gjwlThirdpartyBillno;
    
    /**
     * 来源系统 (固定值，传"广交云供应链管理系统")
     */
    @JsonProperty("gjwl_sourcesystemtype")
    private String gjwlSourcesystemtype;
    
    /**
     * 供应商.编码
     */
    @JsonProperty("gjwl_supplier_number")
    private String gjwlSupplierNumber;
    
    /**
     * 物料明细
     */
    @JsonProperty("entryentity")
    private List<PurPriceAdjustEntry> entryentity;
    
    // 常量定义
    
    /**
     * 组织编码默认值
     */
    public static final String ORG_DEFAULT = "1011";
    
    /**
     * 来源系统默认值
     */
    public static final String SOURCE_SYSTEM = "广交云供应链管理系统";
    
    /**
     * 调价类型 - 入库调价
     */
    public static final String TYPE_INSTOCK = "0";
    
    /**
     * 调价类型 - 返利调价
     */
    public static final String TYPE_REBATE = "1";
}
